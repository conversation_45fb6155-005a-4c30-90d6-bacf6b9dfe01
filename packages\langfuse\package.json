{"name": "@roo-code/langfuse", "description": "Langfuse integration for LLM observability and tracing in Roo Code.", "version": "0.0.0", "type": "module", "main": "./src/index.ts", "exports": {".": {"types": "./src/index.ts", "import": "./src/index.ts", "default": "./src/index.ts"}}, "scripts": {"lint": "eslint src --ext=ts --max-warnings=0", "check-types": "tsc --noEmit", "test": "vitest run", "clean": "rimraf dist .turbo"}, "dependencies": {"@roo-code/types": "workspace:^", "langfuse": "^3.30.0", "zod": "^3.25.61"}, "devDependencies": {"@roo-code/config-eslint": "workspace:^", "@roo-code/config-typescript": "workspace:^", "@types/node": "20.x", "@types/vscode": "^1.84.0", "vitest": "^3.2.3"}}